"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_workflow_toolImplementations_ts";
exports.ids = ["_rsc_src_lib_workflow_toolImplementations_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/oauth/config.ts":
/*!*********************************!*\
  !*** ./src/lib/oauth/config.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TOOL_DESCRIPTIONS: () => (/* binding */ TOOL_DESCRIPTIONS),\n/* harmony export */   TOOL_DISPLAY_NAMES: () => (/* binding */ TOOL_DISPLAY_NAMES),\n/* harmony export */   TOOL_EMOJIS: () => (/* binding */ TOOL_EMOJIS),\n/* harmony export */   TOOL_ICONS: () => (/* binding */ TOOL_ICONS),\n/* harmony export */   generateAuthUrl: () => (/* binding */ generateAuthUrl),\n/* harmony export */   getOAuthConfigForTool: () => (/* binding */ getOAuthConfigForTool),\n/* harmony export */   getToolOAuthConfigs: () => (/* binding */ getToolOAuthConfigs),\n/* harmony export */   validateOAuthConfig: () => (/* binding */ validateOAuthConfig)\n/* harmony export */ });\n// OAuth Configuration for Tool Integrations\n// This file contains OAuth configurations for all supported tools\n// Get the base URL for redirects\nconst getBaseUrl = ()=>{\n    if (false) {}\n    // Server-side detection\n    if (false) {}\n    return process.env.NEXTAUTH_URL || 'http://localhost:3000';\n};\n// Google OAuth configuration for tools\nconst getGoogleToolsConfig = ()=>({\n        clientId: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_ID || process.env.GOOGLE_CLIENT_ID || '',\n        clientSecret: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET || '',\n        authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',\n        tokenUrl: 'https://oauth2.googleapis.com/token',\n        scopes: [\n            'https://www.googleapis.com/auth/drive',\n            'https://www.googleapis.com/auth/documents',\n            'https://www.googleapis.com/auth/spreadsheets',\n            'https://www.googleapis.com/auth/gmail.modify',\n            'https://www.googleapis.com/auth/calendar',\n            'https://www.googleapis.com/auth/youtube'\n        ],\n        redirectUri: process.env.GOOGLE_TOOLS_OAUTH_REDIRECT_URI || `${getBaseUrl()}/api/auth/tools/google/callback`,\n        additionalParams: {\n            access_type: 'offline',\n            prompt: 'consent',\n            include_granted_scopes: 'true'\n        }\n    });\n// Notion OAuth configuration\nconst getNotionConfig = ()=>({\n        clientId: process.env.NOTION_OAUTH_CLIENT_ID || '',\n        clientSecret: process.env.NOTION_OAUTH_CLIENT_SECRET || '',\n        authorizationUrl: 'https://api.notion.com/v1/oauth/authorize',\n        tokenUrl: 'https://api.notion.com/v1/oauth/token',\n        scopes: [],\n        redirectUri: process.env.NOTION_OAUTH_REDIRECT_URI || `${getBaseUrl()}/api/auth/tools/notion/callback`,\n        additionalParams: {\n            owner: 'user',\n            response_type: 'code'\n        }\n    });\n// Tool-specific OAuth configurations\nconst getToolOAuthConfigs = ()=>{\n    const googleConfig = getGoogleToolsConfig();\n    return {\n        // Google services all use the same OAuth config with different scopes\n        google_drive: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/drive'\n            ]\n        },\n        google_docs: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/documents'\n            ]\n        },\n        google_sheets: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/spreadsheets'\n            ]\n        },\n        gmail: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/gmail.modify'\n            ]\n        },\n        calendar: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/calendar'\n            ]\n        },\n        youtube: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/youtube'\n            ]\n        },\n        notion: getNotionConfig(),\n        supabase: {\n            clientId: '',\n            clientSecret: '',\n            authorizationUrl: '',\n            tokenUrl: '',\n            scopes: [],\n            redirectUri: '',\n            additionalParams: {}\n        }\n    };\n};\n// Get OAuth config for a specific tool\nconst getOAuthConfigForTool = (toolType)=>{\n    const configs = getToolOAuthConfigs();\n    return configs[toolType] || null;\n};\n// Validate OAuth configuration\nconst validateOAuthConfig = (config)=>{\n    return !!(config.clientId && config.clientSecret && config.authorizationUrl && config.tokenUrl && config.redirectUri);\n};\n// Generate OAuth authorization URL\nconst generateAuthUrl = (toolType, state)=>{\n    const config = getOAuthConfigForTool(toolType);\n    if (!config || !validateOAuthConfig(config)) {\n        return null;\n    }\n    const params = new URLSearchParams({\n        client_id: config.clientId,\n        redirect_uri: config.redirectUri,\n        response_type: 'code',\n        scope: config.scopes.join(' '),\n        state,\n        ...config.additionalParams\n    });\n    return `${config.authorizationUrl}?${params.toString()}`;\n};\n// Tool display names\nconst TOOL_DISPLAY_NAMES = {\n    google_drive: 'Google Drive',\n    google_docs: 'Google Docs',\n    google_sheets: 'Google Sheets',\n    gmail: 'Gmail',\n    calendar: 'Google Calendar',\n    youtube: 'YouTube',\n    notion: 'Notion',\n    supabase: 'Supabase'\n};\n// Tool icons - Professional logos from reliable sources\nconst TOOL_ICONS = {\n    google_drive: 'https://cloud.gmelius.com/public/logos/google/Google_Drive_Logo.svg',\n    google_docs: 'https://cloud.gmelius.com/public/logos/google/Google_Docs_Logo.svg',\n    google_sheets: 'https://cloud.gmelius.com/public/logos/google/Google_Sheets_Logo.svg',\n    gmail: 'https://cloud.gmelius.com/public/logos/google/Gmail_Logo.svg',\n    calendar: 'https://cloud.gmelius.com/public/logos/google/Google_Calendar_Logo.svg',\n    youtube: 'https://upload.wikimedia.org/wikipedia/commons/0/09/YouTube_full-color_icon_%282017%29.svg',\n    notion: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png',\n    supabase: 'https://cdn.jsdelivr.net/npm/simple-icons@v11/icons/supabase.svg'\n};\n// Tool emoji fallbacks for places where images can't be used (like select options)\nconst TOOL_EMOJIS = {\n    google_drive: '📁',\n    google_docs: '📄',\n    google_sheets: '📊',\n    gmail: '📧',\n    calendar: '📅',\n    youtube: '📺',\n    notion: '📝',\n    supabase: '🗄️'\n};\n// Tool descriptions\nconst TOOL_DESCRIPTIONS = {\n    google_drive: 'Access and manage Google Drive files',\n    google_docs: 'Create and edit Google Documents',\n    google_sheets: 'Work with Google Spreadsheets',\n    gmail: 'Send and manage emails',\n    calendar: 'Manage calendar events and schedules',\n    youtube: 'Access YouTube data and analytics',\n    notion: 'Access Notion databases and pages',\n    supabase: 'Direct database operations'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/oauth/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/oauth/middleware.ts":
/*!*************************************!*\
  !*** ./src/lib/oauth/middleware.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticatedDelete: () => (/* binding */ authenticatedDelete),\n/* harmony export */   authenticatedGet: () => (/* binding */ authenticatedGet),\n/* harmony export */   authenticatedPatch: () => (/* binding */ authenticatedPatch),\n/* harmony export */   authenticatedPost: () => (/* binding */ authenticatedPost),\n/* harmony export */   authenticatedPut: () => (/* binding */ authenticatedPut),\n/* harmony export */   getAuthenticatedHeaders: () => (/* binding */ getAuthenticatedHeaders),\n/* harmony export */   getConnectedTools: () => (/* binding */ getConnectedTools),\n/* harmony export */   hasToolAccess: () => (/* binding */ hasToolAccess),\n/* harmony export */   makeAuthenticatedRequest: () => (/* binding */ makeAuthenticatedRequest),\n/* harmony export */   validateToolConnection: () => (/* binding */ validateToolConnection)\n/* harmony export */ });\n/* harmony import */ var _tokenManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tokenManager */ \"(rsc)/./src/lib/oauth/tokenManager.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(rsc)/./src/lib/oauth/config.ts\");\n// OAuth Authentication Middleware\n// Provides automatic token refresh and authentication for tool API calls\n\n\n// Get authenticated headers for tool API calls\nasync function getAuthenticatedHeaders(userId, toolType, additionalHeaders = {}) {\n    try {\n        console.log(`🔐 AUTH MIDDLEWARE: Getting authenticated headers for ${toolType}`);\n        // Get valid access token (with automatic refresh)\n        const accessToken = await (0,_tokenManager__WEBPACK_IMPORTED_MODULE_0__.getValidAccessToken)(userId, toolType);\n        if (!accessToken) {\n            console.error(`🔐 AUTH MIDDLEWARE: No valid access token for ${toolType}`);\n            await (0,_tokenManager__WEBPACK_IMPORTED_MODULE_0__.updateConnectionStatus)(userId, toolType, 'expired');\n            return {\n                headers: {},\n                isAuthenticated: false,\n                error: `${_config__WEBPACK_IMPORTED_MODULE_1__.TOOL_DISPLAY_NAMES[toolType] || toolType} connection expired. Please reconnect.`\n            };\n        }\n        // Prepare headers based on tool type\n        const headers = {\n            'Authorization': `Bearer ${accessToken}`,\n            'Content-Type': 'application/json',\n            ...additionalHeaders\n        };\n        // Tool-specific header adjustments\n        switch(toolType){\n            case 'notion':\n                headers['Notion-Version'] = '2022-06-28';\n                break;\n            case 'google_drive':\n            case 'google_docs':\n            case 'google_sheets':\n            case 'gmail':\n            case 'calendar':\n            case 'youtube':\n                break;\n            default:\n                break;\n        }\n        console.log(`🔐 AUTH MIDDLEWARE: Headers prepared for ${toolType}`);\n        return {\n            headers,\n            isAuthenticated: true\n        };\n    } catch (error) {\n        console.error(`🔐 AUTH MIDDLEWARE: Error getting authenticated headers for ${toolType}:`, error);\n        await (0,_tokenManager__WEBPACK_IMPORTED_MODULE_0__.updateConnectionStatus)(userId, toolType, 'error');\n        return {\n            headers: {},\n            isAuthenticated: false,\n            error: 'Authentication error occurred'\n        };\n    }\n}\n// Make authenticated API request to tool\nasync function makeAuthenticatedRequest(url, options) {\n    const { userId, toolType, method = 'GET', body, additionalHeaders, timeout = 30000 } = options;\n    console.log(`🔐 AUTH MIDDLEWARE: Making authenticated ${method} request to ${toolType}`);\n    // Get authenticated headers\n    const authResult = await getAuthenticatedHeaders(userId, toolType, additionalHeaders);\n    if (!authResult.isAuthenticated) {\n        throw new Error(authResult.error || 'Authentication failed');\n    }\n    // Prepare request options\n    const requestOptions = {\n        method,\n        headers: authResult.headers,\n        signal: AbortSignal.timeout(timeout)\n    };\n    if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {\n        requestOptions.body = typeof body === 'string' ? body : JSON.stringify(body);\n    }\n    try {\n        const response = await fetch(url, requestOptions);\n        // Handle authentication errors\n        if (response.status === 401 || response.status === 403) {\n            console.error(`🔐 AUTH MIDDLEWARE: Authentication failed for ${toolType} (${response.status})`);\n            await (0,_tokenManager__WEBPACK_IMPORTED_MODULE_0__.updateConnectionStatus)(userId, toolType, 'expired');\n            throw new Error(`${_config__WEBPACK_IMPORTED_MODULE_1__.TOOL_DISPLAY_NAMES[toolType] || toolType} authentication failed. Please reconnect.`);\n        }\n        // Handle rate limiting\n        if (response.status === 429) {\n            console.warn(`🔐 AUTH MIDDLEWARE: Rate limited for ${toolType}`);\n            const retryAfter = response.headers.get('Retry-After');\n            throw new Error(`Rate limited. ${retryAfter ? `Retry after ${retryAfter} seconds.` : 'Please try again later.'}`);\n        }\n        console.log(`🔐 AUTH MIDDLEWARE: Request successful for ${toolType} (${response.status})`);\n        return response;\n    } catch (error) {\n        if (error instanceof Error) {\n            if (error.name === 'AbortError') {\n                throw new Error(`Request timeout for ${_config__WEBPACK_IMPORTED_MODULE_1__.TOOL_DISPLAY_NAMES[toolType] || toolType}`);\n            }\n            throw error;\n        }\n        throw new Error(`Request failed for ${_config__WEBPACK_IMPORTED_MODULE_1__.TOOL_DISPLAY_NAMES[toolType] || toolType}`);\n    }\n}\n// Wrapper for GET requests\nasync function authenticatedGet(url, options) {\n    return makeAuthenticatedRequest(url, {\n        ...options,\n        method: 'GET'\n    });\n}\n// Wrapper for POST requests\nasync function authenticatedPost(url, body, options) {\n    return makeAuthenticatedRequest(url, {\n        ...options,\n        method: 'POST',\n        body\n    });\n}\n// Wrapper for PUT requests\nasync function authenticatedPut(url, body, options) {\n    return makeAuthenticatedRequest(url, {\n        ...options,\n        method: 'PUT',\n        body\n    });\n}\n// Wrapper for PATCH requests\nasync function authenticatedPatch(url, body, options) {\n    return makeAuthenticatedRequest(url, {\n        ...options,\n        method: 'PATCH',\n        body\n    });\n}\n// Wrapper for DELETE requests\nasync function authenticatedDelete(url, options) {\n    return makeAuthenticatedRequest(url, {\n        ...options,\n        method: 'DELETE'\n    });\n}\n// Check if user has access to a specific tool\nasync function hasToolAccess(userId, toolType) {\n    try {\n        const accessToken = await (0,_tokenManager__WEBPACK_IMPORTED_MODULE_0__.getValidAccessToken)(userId, toolType);\n        return !!accessToken;\n    } catch (error) {\n        console.error(`🔐 AUTH MIDDLEWARE: Error checking tool access for ${toolType}:`, error);\n        return false;\n    }\n}\n// Get all connected tools for a user\nasync function getConnectedTools(userId) {\n    try {\n        const allTools = Object.keys(_config__WEBPACK_IMPORTED_MODULE_1__.TOOL_DISPLAY_NAMES);\n        const connectedTools = [];\n        for (const toolType of allTools){\n            const hasAccess = await hasToolAccess(userId, toolType);\n            if (hasAccess) {\n                connectedTools.push(toolType);\n            }\n        }\n        console.log(`🔐 AUTH MIDDLEWARE: User ${userId} has ${connectedTools.length} connected tools`);\n        return connectedTools;\n    } catch (error) {\n        console.error('🔐 AUTH MIDDLEWARE: Error getting connected tools:', error);\n        return [];\n    }\n}\n// Validate tool connection before use\nasync function validateToolConnection(userId, toolType) {\n    try {\n        const authResult = await getAuthenticatedHeaders(userId, toolType);\n        if (!authResult.isAuthenticated) {\n            return {\n                isValid: false,\n                error: authResult.error || 'Tool not connected'\n            };\n        }\n        return {\n            isValid: true\n        };\n    } catch (error) {\n        console.error(`🔐 AUTH MIDDLEWARE: Error validating tool connection for ${toolType}:`, error);\n        return {\n            isValid: false,\n            error: 'Connection validation failed'\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/oauth/middleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/oauth/tokenManager.ts":
/*!***************************************!*\
  !*** ./src/lib/oauth/tokenManager.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getOAuthTokens: () => (/* binding */ getOAuthTokens),\n/* harmony export */   getUserToolConnections: () => (/* binding */ getUserToolConnections),\n/* harmony export */   getValidAccessToken: () => (/* binding */ getValidAccessToken),\n/* harmony export */   needsTokenRefresh: () => (/* binding */ needsTokenRefresh),\n/* harmony export */   refreshOAuthToken: () => (/* binding */ refreshOAuthToken),\n/* harmony export */   revokeOAuthConnection: () => (/* binding */ revokeOAuthConnection),\n/* harmony export */   storeOAuthTokens: () => (/* binding */ storeOAuthTokens),\n/* harmony export */   updateConnectionStatus: () => (/* binding */ updateConnectionStatus),\n/* harmony export */   updateLastUsed: () => (/* binding */ updateLastUsed)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(rsc)/./src/lib/oauth/config.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_2__);\n// OAuth Token Management\n// Handles secure storage, retrieval, and refresh of OAuth tokens\n\n\n\n// Encryption utilities\nconst ENCRYPTION_KEY = process.env.OAUTH_ENCRYPTION_KEY || process.env.ROKEY_ENCRYPTION_KEY || '';\nconst ALGORITHM = 'aes-256-gcm';\nfunction encrypt(text) {\n    if (!ENCRYPTION_KEY) {\n        throw new Error('Encryption key not configured');\n    }\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_2___default().randomBytes(16);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createCipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY.slice(0, 32)), iv);\n    let encrypted = cipher.update(text, 'utf8', 'hex');\n    encrypted += cipher.final('hex');\n    const authTag = cipher.getAuthTag();\n    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;\n}\nfunction decrypt(encryptedText) {\n    if (!ENCRYPTION_KEY) {\n        throw new Error('Encryption key not configured');\n    }\n    const parts = encryptedText.split(':');\n    if (parts.length !== 3) {\n        throw new Error('Invalid encrypted data format');\n    }\n    const iv = Buffer.from(parts[0], 'hex');\n    const authTag = Buffer.from(parts[1], 'hex');\n    const encrypted = parts[2];\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createDecipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY.slice(0, 32)), iv);\n    decipher.setAuthTag(authTag);\n    let decrypted = decipher.update(encrypted, 'hex', 'utf8');\n    decrypted += decipher.final('utf8');\n    return decrypted;\n}\n// Store OAuth tokens securely\nasync function storeOAuthTokens(userId, toolType, tokenData) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        // Calculate expiration time\n        const expiresAt = tokenData.expires_in ? new Date(Date.now() + tokenData.expires_in * 1000) : null;\n        // Parse scopes\n        const scopes = tokenData.scope ? tokenData.scope.split(' ') : [];\n        // Encrypt sensitive data\n        const encryptedAccessToken = encrypt(tokenData.access_token);\n        const encryptedRefreshToken = tokenData.refresh_token ? encrypt(tokenData.refresh_token) : null;\n        const { data, error } = await supabase.from('tool_oauth_connections').upsert({\n            user_id: userId,\n            tool_type: toolType,\n            access_token: encryptedAccessToken,\n            refresh_token: encryptedRefreshToken,\n            expires_at: expiresAt?.toISOString(),\n            scopes,\n            provider_user_id: tokenData.provider_user_id,\n            provider_user_email: tokenData.provider_user_email,\n            provider_user_name: tokenData.provider_user_name,\n            connection_status: 'connected',\n            last_used_at: new Date().toISOString()\n        }, {\n            onConflict: 'user_id,tool_type'\n        }).select().single();\n        if (error) {\n            console.error('Error storing OAuth tokens:', error);\n            return null;\n        }\n        return {\n            ...data,\n            access_token: tokenData.access_token,\n            refresh_token: tokenData.refresh_token,\n            expires_at: expiresAt,\n            created_at: new Date(data.created_at),\n            updated_at: new Date(data.updated_at)\n        };\n    } catch (error) {\n        console.error('Error in storeOAuthTokens:', error);\n        return null;\n    }\n}\n// Retrieve OAuth tokens for a user and tool\nasync function getOAuthTokens(userId, toolType) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        const { data, error } = await supabase.from('tool_oauth_connections').select('*').eq('user_id', userId).eq('tool_type', toolType).eq('connection_status', 'connected').single();\n        if (error || !data) {\n            return null;\n        }\n        // Decrypt sensitive data\n        const accessToken = decrypt(data.access_token);\n        const refreshToken = data.refresh_token ? decrypt(data.refresh_token) : undefined;\n        return {\n            ...data,\n            access_token: accessToken,\n            refresh_token: refreshToken,\n            expires_at: data.expires_at ? new Date(data.expires_at) : undefined,\n            created_at: new Date(data.created_at),\n            updated_at: new Date(data.updated_at)\n        };\n    } catch (error) {\n        console.error('Error retrieving OAuth tokens:', error);\n        return null;\n    }\n}\n// Check if token needs refresh\nfunction needsTokenRefresh(tokenData) {\n    if (!tokenData.expires_at) {\n        return false; // No expiration info, assume it's valid\n    }\n    // Refresh if token expires within 5 minutes\n    const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);\n    return tokenData.expires_at <= fiveMinutesFromNow;\n}\n// Refresh OAuth token\nasync function refreshOAuthToken(userId, toolType) {\n    try {\n        const tokenData = await getOAuthTokens(userId, toolType);\n        if (!tokenData || !tokenData.refresh_token) {\n            return {\n                success: false,\n                error: 'No refresh token available'\n            };\n        }\n        const config = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getOAuthConfigForTool)(toolType);\n        if (!config) {\n            return {\n                success: false,\n                error: 'OAuth configuration not found'\n            };\n        }\n        // Prepare refresh request\n        const refreshData = new URLSearchParams({\n            grant_type: 'refresh_token',\n            refresh_token: tokenData.refresh_token,\n            client_id: config.clientId,\n            client_secret: config.clientSecret\n        });\n        const response = await fetch(config.tokenUrl, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/x-www-form-urlencoded'\n            },\n            body: refreshData\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Token refresh failed:', errorText);\n            return {\n                success: false,\n                error: 'Token refresh failed'\n            };\n        }\n        const refreshResult = await response.json();\n        // Store updated tokens\n        const updatedTokenData = await storeOAuthTokens(userId, toolType, {\n            access_token: refreshResult.access_token,\n            refresh_token: refreshResult.refresh_token || tokenData.refresh_token,\n            expires_in: refreshResult.expires_in,\n            scope: refreshResult.scope || tokenData.scopes.join(' '),\n            provider_user_id: tokenData.provider_user_id,\n            provider_user_email: tokenData.provider_user_email,\n            provider_user_name: tokenData.provider_user_name\n        });\n        if (!updatedTokenData) {\n            return {\n                success: false,\n                error: 'Failed to store refreshed tokens'\n            };\n        }\n        return {\n            success: true,\n            access_token: refreshResult.access_token,\n            refresh_token: refreshResult.refresh_token || tokenData.refresh_token,\n            expires_at: updatedTokenData.expires_at\n        };\n    } catch (error) {\n        console.error('Error refreshing OAuth token:', error);\n        return {\n            success: false,\n            error: 'Token refresh error'\n        };\n    }\n}\n// Get valid access token (with automatic refresh)\nasync function getValidAccessToken(userId, toolType) {\n    try {\n        let tokenData = await getOAuthTokens(userId, toolType);\n        if (!tokenData) {\n            return null;\n        }\n        // Check if token needs refresh\n        if (needsTokenRefresh(tokenData)) {\n            const refreshResult = await refreshOAuthToken(userId, toolType);\n            if (refreshResult.success && refreshResult.access_token) {\n                return refreshResult.access_token;\n            } else {\n                // Mark token as expired\n                await updateConnectionStatus(userId, toolType, 'expired');\n                return null;\n            }\n        }\n        // Update last used timestamp\n        await updateLastUsed(userId, toolType);\n        return tokenData.access_token;\n    } catch (error) {\n        console.error('Error getting valid access token:', error);\n        return null;\n    }\n}\n// Update connection status\nasync function updateConnectionStatus(userId, toolType, status) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        const { error } = await supabase.from('tool_oauth_connections').update({\n            connection_status: status\n        }).eq('user_id', userId).eq('tool_type', toolType);\n        return !error;\n    } catch (error) {\n        console.error('Error updating connection status:', error);\n        return false;\n    }\n}\n// Update last used timestamp\nasync function updateLastUsed(userId, toolType) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        const { error } = await supabase.from('tool_oauth_connections').update({\n            last_used_at: new Date().toISOString()\n        }).eq('user_id', userId).eq('tool_type', toolType);\n        return !error;\n    } catch (error) {\n        console.error('Error updating last used timestamp:', error);\n        return false;\n    }\n}\n// Revoke OAuth connection\nasync function revokeOAuthConnection(userId, toolType) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        const { error } = await supabase.from('tool_oauth_connections').delete().eq('user_id', userId).eq('tool_type', toolType);\n        return !error;\n    } catch (error) {\n        console.error('Error revoking OAuth connection:', error);\n        return false;\n    }\n}\n// Get all tool connections for a user\nasync function getUserToolConnections(userId) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServiceRoleClient)();\n        const { data, error } = await supabase.from('tool_oauth_connections').select('*').eq('user_id', userId).order('created_at', {\n            ascending: false\n        });\n        if (error || !data) {\n            return [];\n        }\n        return data.map((item)=>({\n                ...item,\n                access_token: '',\n                refresh_token: undefined,\n                expires_at: item.expires_at ? new Date(item.expires_at) : undefined,\n                created_at: new Date(item.created_at),\n                updated_at: new Date(item.updated_at)\n            }));\n    } catch (error) {\n        console.error('Error getting user tool connections:', error);\n        return [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/oauth/tokenManager.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/workflow/toolImplementations.ts":
/*!*************************************************!*\
  !*** ./src/lib/workflow/toolImplementations.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GmailAPI: () => (/* binding */ GmailAPI),\n/* harmony export */   GoogleDocsAPI: () => (/* binding */ GoogleDocsAPI),\n/* harmony export */   GoogleDriveAPI: () => (/* binding */ GoogleDriveAPI),\n/* harmony export */   GoogleSheetsAPI: () => (/* binding */ GoogleSheetsAPI)\n/* harmony export */ });\n/* harmony import */ var _lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/oauth/middleware */ \"(rsc)/./src/lib/oauth/middleware.ts\");\n// Tool Implementation Methods\n// Contains the actual API implementations for each tool\n\n// Google Drive API implementations\nclass GoogleDriveAPI {\n    static async listFiles(userId, params, timeout) {\n        const url = 'https://www.googleapis.com/drive/v3/files';\n        const queryParams = new URLSearchParams({\n            pageSize: params.limit || '10',\n            fields: 'files(id,name,mimeType,size,modifiedTime,webViewLink)',\n            ...params.query && {\n                q: params.query\n            }\n        });\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedGet)(`${url}?${queryParams}`, {\n            userId,\n            toolType: 'google_drive',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Drive API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            files: data.files,\n            count: data.files?.length || 0\n        };\n    }\n    static async getFile(userId, params, timeout) {\n        const { fileId } = params;\n        if (!fileId) throw new Error('File ID is required');\n        const url = `https://www.googleapis.com/drive/v3/files/${fileId}`;\n        const queryParams = new URLSearchParams({\n            fields: 'id,name,mimeType,size,modifiedTime,webViewLink,parents'\n        });\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedGet)(`${url}?${queryParams}`, {\n            userId,\n            toolType: 'google_drive',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Drive API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            file: data\n        };\n    }\n    static async createFile(userId, params, timeout) {\n        const { name, content, mimeType = 'text/plain', parentId } = params;\n        if (!name) throw new Error('File name is required');\n        const metadata = {\n            name,\n            ...parentId && {\n                parents: [\n                    parentId\n                ]\n            }\n        };\n        const url = 'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart';\n        // Create multipart body\n        const boundary = '-------314159265358979323846';\n        const delimiter = `\\r\\n--${boundary}\\r\\n`;\n        const close_delim = `\\r\\n--${boundary}--`;\n        let body = delimiter;\n        body += 'Content-Type: application/json\\r\\n\\r\\n';\n        body += JSON.stringify(metadata) + delimiter;\n        body += `Content-Type: ${mimeType}\\r\\n\\r\\n`;\n        body += content || '';\n        body += close_delim;\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedPost)(url, body, {\n            userId,\n            toolType: 'google_drive',\n            additionalHeaders: {\n                'Content-Type': `multipart/related; boundary=\"${boundary}\"`\n            },\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Drive API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            file: data\n        };\n    }\n    static async searchFiles(userId, params, timeout) {\n        const { query, limit = 10 } = params;\n        if (!query) throw new Error('Search query is required');\n        const url = 'https://www.googleapis.com/drive/v3/files';\n        const queryParams = new URLSearchParams({\n            q: `name contains '${query}' or fullText contains '${query}'`,\n            pageSize: limit.toString(),\n            fields: 'files(id,name,mimeType,size,modifiedTime,webViewLink)'\n        });\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedGet)(`${url}?${queryParams}`, {\n            userId,\n            toolType: 'google_drive',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Drive API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            files: data.files,\n            query,\n            count: data.files?.length || 0\n        };\n    }\n}\n// Google Docs API implementations\nclass GoogleDocsAPI {\n    static async createDocument(userId, params, timeout) {\n        const { title } = params;\n        if (!title) throw new Error('Document title is required');\n        const url = 'https://docs.googleapis.com/v1/documents';\n        const body = {\n            title\n        };\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedPost)(url, body, {\n            userId,\n            toolType: 'google_docs',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Docs API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            document: data\n        };\n    }\n    static async getDocument(userId, params, timeout) {\n        const { documentId } = params;\n        if (!documentId) throw new Error('Document ID is required');\n        const url = `https://docs.googleapis.com/v1/documents/${documentId}`;\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedGet)(url, {\n            userId,\n            toolType: 'google_docs',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Docs API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            document: data\n        };\n    }\n    static async updateDocument(userId, params, timeout) {\n        const { documentId, requests } = params;\n        if (!documentId) throw new Error('Document ID is required');\n        if (!requests) throw new Error('Update requests are required');\n        const url = `https://docs.googleapis.com/v1/documents/${documentId}:batchUpdate`;\n        const body = {\n            requests\n        };\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedPost)(url, body, {\n            userId,\n            toolType: 'google_docs',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Docs API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            result: data\n        };\n    }\n}\n// Google Sheets API implementations\nclass GoogleSheetsAPI {\n    static async createSpreadsheet(userId, params, timeout) {\n        const { title, sheets } = params;\n        if (!title) throw new Error('Spreadsheet title is required');\n        const url = 'https://sheets.googleapis.com/v4/spreadsheets';\n        const body = {\n            properties: {\n                title\n            },\n            ...sheets && {\n                sheets\n            }\n        };\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedPost)(url, body, {\n            userId,\n            toolType: 'google_sheets',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Sheets API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            spreadsheet: data\n        };\n    }\n    static async getSpreadsheet(userId, params, timeout) {\n        const { spreadsheetId, ranges } = params;\n        if (!spreadsheetId) throw new Error('Spreadsheet ID is required');\n        let url = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}`;\n        if (ranges) {\n            const rangeParams = Array.isArray(ranges) ? ranges.join('&ranges=') : ranges;\n            url += `?ranges=${rangeParams}`;\n        }\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedGet)(url, {\n            userId,\n            toolType: 'google_sheets',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Sheets API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            spreadsheet: data\n        };\n    }\n    static async updateCells(userId, params, timeout) {\n        const { spreadsheetId, range, values } = params;\n        if (!spreadsheetId) throw new Error('Spreadsheet ID is required');\n        if (!range) throw new Error('Range is required');\n        if (!values) throw new Error('Values are required');\n        const url = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${range}?valueInputOption=RAW`;\n        const body = {\n            values\n        };\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedPut)(url, body, {\n            userId,\n            toolType: 'google_sheets',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Sheets API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            result: data\n        };\n    }\n    static async readRange(userId, params, timeout) {\n        const { spreadsheetId, range } = params;\n        if (!spreadsheetId) throw new Error('Spreadsheet ID is required');\n        if (!range) throw new Error('Range is required');\n        const url = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${range}`;\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedGet)(url, {\n            userId,\n            toolType: 'google_sheets',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Sheets API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            values: data.values || [],\n            range: data.range\n        };\n    }\n    static async appendRow(userId, params, timeout) {\n        const { spreadsheetId, range, values } = params;\n        if (!spreadsheetId) throw new Error('Spreadsheet ID is required');\n        if (!range) throw new Error('Range is required');\n        if (!values) throw new Error('Values are required');\n        const url = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${range}:append?valueInputOption=RAW`;\n        const body = {\n            values: [\n                values\n            ]\n        };\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedPost)(url, body, {\n            userId,\n            toolType: 'google_sheets',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Google Sheets API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            result: data\n        };\n    }\n}\n// Gmail API implementations\nclass GmailAPI {\n    static async sendEmail(userId, params, timeout) {\n        const { to, subject, body, from } = params;\n        if (!to) throw new Error('Recipient email is required');\n        if (!subject) throw new Error('Email subject is required');\n        if (!body) throw new Error('Email body is required');\n        // Create email message\n        const email = [\n            `To: ${to}`,\n            `Subject: ${subject}`,\n            '',\n            body\n        ].join('\\n');\n        const encodedEmail = Buffer.from(email).toString('base64').replace(/\\+/g, '-').replace(/\\//g, '_');\n        const url = 'https://gmail.googleapis.com/gmail/v1/users/me/messages/send';\n        const requestBody = {\n            raw: encodedEmail\n        };\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedPost)(url, requestBody, {\n            userId,\n            toolType: 'gmail',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Gmail API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            message: data,\n            sent_to: to,\n            subject\n        };\n    }\n    static async listEmails(userId, params, timeout) {\n        const { maxResults = 10, query } = params;\n        let url = `https://gmail.googleapis.com/gmail/v1/users/me/messages?maxResults=${maxResults}`;\n        if (query) {\n            url += `&q=${encodeURIComponent(query)}`;\n        }\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedGet)(url, {\n            userId,\n            toolType: 'gmail',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Gmail API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            messages: data.messages || [],\n            resultSizeEstimate: data.resultSizeEstimate\n        };\n    }\n    static async getEmail(userId, params, timeout) {\n        const { messageId } = params;\n        if (!messageId) throw new Error('Message ID is required');\n        const url = `https://gmail.googleapis.com/gmail/v1/users/me/messages/${messageId}`;\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedGet)(url, {\n            userId,\n            toolType: 'gmail',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Gmail API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            message: data\n        };\n    }\n    static async searchEmails(userId, params, timeout) {\n        const { query, maxResults = 10 } = params;\n        if (!query) throw new Error('Search query is required');\n        const url = `https://gmail.googleapis.com/gmail/v1/users/me/messages?maxResults=${maxResults}&q=${encodeURIComponent(query)}`;\n        const response = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.authenticatedGet)(url, {\n            userId,\n            toolType: 'gmail',\n            timeout\n        });\n        if (!response.ok) {\n            throw new Error(`Gmail API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            messages: data.messages || [],\n            query,\n            resultSizeEstimate: data.resultSizeEstimate\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/workflow/toolImplementations.ts\n");

/***/ })

};
;